#!/usr/bin/env python3
"""
Test script to verify AG-UI compatible agents work correctly.
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_agui_agents():
    """Test AG-UI compatible agents."""
    try:
        print("🧪 Testing AG-UI Compatible Agents...")
        
        # Test imports
        from ag_ui.core import RunAgentInput
        from ag_ui.core import Message as AGUIMessage
        from n8n_builder.agents.base_agent import (
            AgentConfig,
            WorkflowGeneratorAgent,
            ValidationAgent,
            OrchestratorAgent
        )
        print("✅ AG-UI imports successful")
        
        # Create agent config
        config = AgentConfig(
            name="test_workflow_generator",
            capabilities={
                "workflow_generation": True,
                "ag_ui_protocol": True
            }
        )
        
        # Create workflow generator agent
        generator = WorkflowGeneratorAgent(config)
        await generator.start()
        print("✅ WorkflowGeneratorAgent created and started")
        
        # Create test input with required fields
        test_input = RunAgentInput(
            threadId="test-thread-123",
            runId="test-run-456",
            forwardedProps={},
            messages=[],
            context=[],
            tools=[],
            state=None
        )
        
        # Test AG-UI workflow generation
        print("\n🚀 Testing AG-UI workflow generation...")
        event_count = 0
        async for event in generator.run_agent(test_input):
            event_count += 1
            print(f"📡 Event {event_count}: {event.type}")
            if hasattr(event, 'delta') and event.delta:
                print(f"   Content: {event.delta[:100]}...")
            elif hasattr(event, 'snapshot') and event.snapshot:
                print(f"   State: {dict(event.snapshot)}")
        
        print(f"✅ Generated {event_count} AG-UI events")
        
        # Test validation agent
        print("\n🔍 Testing ValidationAgent...")
        validator = ValidationAgent(AgentConfig(name="test_validator"))
        await validator.start()
        
        validation_count = 0
        async for event in validator.run_agent(test_input):
            validation_count += 1
            if hasattr(event, 'delta') and event.delta:
                print(f"📡 Validation: {event.delta}")
        
        print(f"✅ Generated {validation_count} validation events")
        
        # Clean up
        await generator.close()
        await validator.close()
        
        print(f"\n🎉 AG-UI Agent Test Complete!")
        print(f"   - WorkflowGenerator events: {event_count}")
        print(f"   - Validation events: {validation_count}")
        print(f"   - All agents working with AG-UI protocol!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_agui_agents())
    sys.exit(0 if success else 1)
