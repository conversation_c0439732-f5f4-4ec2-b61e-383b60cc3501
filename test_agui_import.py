#!/usr/bin/env python3
"""
Test script to verify AG-UI protocol imports and basic functionality.
"""

def test_agui_imports():
    """Test AG-UI imports and basic functionality."""
    try:
        # Test basic import
        import ag_ui
        print("✅ ag_ui package imported successfully")
        
        # Test core imports
        from ag_ui.core import Event, EventType, BaseEvent
        print("✅ ag_ui.core imports successful")
        
        # Test specific event types
        from ag_ui.core import (
            RunStartedEvent, 
            RunFinishedEvent, 
            TextMessageStartEvent,
            TextMessageContentEvent,
            TextMessageEndEvent
        )
        print("✅ AG-UI event types imported successfully")
        
        # Test creating a basic event
        event = RunStartedEvent(
            type=EventType.RUN_STARTED,
            thread_id="test-thread",
            run_id="test-run"
        )
        print(f"✅ Created test event: {event.type}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("Testing AG-UI Protocol imports...")
    success = test_agui_imports()
    if success:
        print("\n🎉 AG-UI Protocol is ready for use!")
    else:
        print("\n💥 AG-UI Protocol setup needs attention")
